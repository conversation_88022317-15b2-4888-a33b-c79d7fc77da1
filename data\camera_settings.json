{"192.168.4.243": {"resolution": {"width": 1280, "height": 720}, "onvif_credentials": {"username": "admin", "password": "Admin@123", "port": 80}, "stream_settings": {"streamType": "main", "videoType": null, "bitrateType": null, "videoQuality": "medium", "frameRate": 22, "maxBitrate": 2048, "videoEncoding": "H.265", "profile": null, "iFrameInterval": 50}, "failed_settings": {"frameRate": "Configuration rejected by camera. Hikvision cameras often have ONVIF limitations - try using the camera's web interface for these settings: frameRate, maxBitrate, videoEncoding, iFrameInterval", "maxBitrate": "Configuration rejected by camera. Hikvision cameras often have ONVIF limitations - try using the camera's web interface for these settings: maxBitrate, videoEncoding, iFrameInterval", "videoEncoding": "Configuration rejected by camera. Hikvision cameras often have ONVIF limitations - try using the camera's web interface for these settings: videoEncoding, iFrameInterval", "iFrameInterval": "Configuration rejected by camera. Hikvision cameras often have ONVIF limitations - try using the camera's web interface for these settings: iFrameInterval", "streamType": "Setting 'streamType' requires Hikvision web interface. ONVIF doesn't sync these settings properly with Hikvision cameras. Use camera web interface at http://192.168.4.243", "videoQuality": "Setting 'videoQuality' requires Hikvision web interface. ONVIF doesn't sync these settings properly with Hikvision cameras. Use camera web interface at http://192.168.4.243", "profile": "Setting 'profile' requires Hikvision web interface. ONVIF doesn't sync these settings properly with Hikvision cameras. Use camera web interface at http://192.168.4.243"}}}